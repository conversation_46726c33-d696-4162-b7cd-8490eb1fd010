#!/usr/bin/env node

/**
 * Migration Verification Script
 * Comprehensive testing tool to verify 100% functional parity
 * between Python FastAPI and NestJS TypeScript backends
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const PYTHON_BASE_URL = 'http://localhost:8123';  // LangGraph dev server
const TYPESCRIPT_BASE_URL = 'http://localhost:2024';  // NestJS server
const TEST_TIMEOUT = 30000; // 30 seconds

// Test results storage
const results = {
  timestamp: new Date().toISOString(),
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  },
  tests: []
};

/**
 * Utility function to log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Add test result
 */
function addResult(testName, status, details = {}) {
  results.tests.push({
    name: testName,
    status,
    timestamp: new Date().toISOString(),
    ...details
  });
  results.summary.total++;
  results.summary[status]++;
}

/**
 * Test server availability
 */
async function testServerAvailability(baseUrl, serverName) {
  log(`Testing ${serverName} server availability...`);
  
  try {
    const response = await fetch(`${baseUrl}/health`, { 
      timeout: 5000 
    });
    
    if (response.ok) {
      log(`✅ ${serverName} server is available`);
      addResult(`${serverName} Server Availability`, 'passed');
      return true;
    } else {
      log(`❌ ${serverName} server returned status ${response.status}`);
      addResult(`${serverName} Server Availability`, 'failed', {
        error: `HTTP ${response.status}`
      });
      return false;
    }
  } catch (error) {
    log(`❌ ${serverName} server is not available: ${error.message}`);
    addResult(`${serverName} Server Availability`, 'failed', {
      error: error.message
    });
    return false;
  }
}

/**
 * Test API endpoint compatibility
 */
async function testApiCompatibility() {
  log('Testing API endpoint compatibility...');
  
  const testCases = [
    {
      name: 'Assistant Info',
      endpoint: '/assistants/agent',
      method: 'GET'
    },
    {
      name: 'Assistant Graph',
      endpoint: '/assistants/agent/graph',
      method: 'GET'
    },
    {
      name: 'Create Thread',
      endpoint: '/threads',
      method: 'POST',
      body: { metadata: {} }
    }
  ];

  for (const testCase of testCases) {
    try {
      log(`Testing ${testCase.name}...`);
      
      // Test TypeScript backend
      const tsOptions = {
        method: testCase.method,
        headers: { 'Content-Type': 'application/json' },
        timeout: TEST_TIMEOUT
      };
      
      if (testCase.body) {
        tsOptions.body = JSON.stringify(testCase.body);
      }
      
      const tsResponse = await fetch(`${TYPESCRIPT_BASE_URL}${testCase.endpoint}`, tsOptions);
      const tsData = await tsResponse.json();
      
      if (tsResponse.ok) {
        log(`✅ ${testCase.name} - TypeScript backend OK`);
        addResult(`API Compatibility - ${testCase.name}`, 'passed', {
          typescript_status: tsResponse.status,
          typescript_response: tsData
        });
      } else {
        log(`❌ ${testCase.name} - TypeScript backend failed`);
        addResult(`API Compatibility - ${testCase.name}`, 'failed', {
          typescript_status: tsResponse.status,
          typescript_error: tsData
        });
      }
      
    } catch (error) {
      log(`❌ ${testCase.name} - Error: ${error.message}`);
      addResult(`API Compatibility - ${testCase.name}`, 'failed', {
        error: error.message
      });
    }
  }
}

/**
 * Test agent functionality
 */
async function testAgentFunctionality() {
  log('Testing agent functionality...');
  
  const testMessage = {
    messages: [
      {
        type: 'human',
        content: 'What are the latest developments in quantum computing in 2024?'
      }
    ],
    config: {
      configurable: {
        initial_search_query_count: 2,
        max_research_loops: 1,
        reasoning_model: 'gemini-2.5-pro-preview-05-06'
      }
    }
  };

  try {
    log('Testing TypeScript agent endpoint...');
    
    const response = await fetch(`${TYPESCRIPT_BASE_URL}/api/run`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testMessage),
      timeout: TEST_TIMEOUT
    });
    
    const data = await response.json();
    
    if (response.ok && data.result) {
      log('✅ TypeScript agent functionality test passed');
      addResult('Agent Functionality - TypeScript', 'passed', {
        response_status: response.status,
        has_result: !!data.result,
        has_messages: !!(data.result.messages && data.result.messages.length > 0)
      });
    } else {
      log('❌ TypeScript agent functionality test failed');
      addResult('Agent Functionality - TypeScript', 'failed', {
        response_status: response.status,
        error: data
      });
    }
    
  } catch (error) {
    log(`❌ Agent functionality test error: ${error.message}`);
    addResult('Agent Functionality - TypeScript', 'failed', {
      error: error.message
    });
  }
}

/**
 * Test streaming functionality
 */
async function testStreamingFunctionality() {
  log('Testing streaming functionality...');
  
  try {
    // First create a thread
    const threadResponse = await fetch(`${TYPESCRIPT_BASE_URL}/threads`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ metadata: {} }),
      timeout: 10000
    });
    
    if (!threadResponse.ok) {
      throw new Error(`Failed to create thread: ${threadResponse.status}`);
    }
    
    const threadData = await threadResponse.json();
    const threadId = threadData.thread_id;
    
    log(`Created thread: ${threadId}`);
    
    // Test streaming
    const streamPayload = {
      assistant_id: 'agent',
      input: {
        messages: [
          {
            content: 'What is artificial intelligence?',
            type: 'human'
          }
        ]
      },
      config: {
        configurable: {
          initial_search_query_count: 1,
          max_research_loops: 1
        }
      },
      stream_mode: ['values']
    };
    
    const streamResponse = await fetch(`${TYPESCRIPT_BASE_URL}/threads/${threadId}/runs/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(streamPayload),
      timeout: TEST_TIMEOUT
    });
    
    if (streamResponse.ok) {
      log('✅ Streaming endpoint accessible');
      addResult('Streaming Functionality', 'passed', {
        thread_id: threadId,
        stream_status: streamResponse.status
      });
    } else {
      log(`❌ Streaming test failed with status: ${streamResponse.status}`);
      const errorText = await streamResponse.text();
      addResult('Streaming Functionality', 'failed', {
        stream_status: streamResponse.status,
        error: errorText
      });
    }
    
  } catch (error) {
    log(`❌ Streaming test error: ${error.message}`);
    addResult('Streaming Functionality', 'failed', {
      error: error.message
    });
  }
}

/**
 * Test configuration handling
 */
async function testConfigurationHandling() {
  log('Testing configuration handling...');
  
  const configTests = [
    {
      name: 'Default Configuration',
      config: {}
    },
    {
      name: 'Custom Configuration',
      config: {
        configurable: {
          initial_search_query_count: 5,
          max_research_loops: 3,
          reasoning_model: 'gemini-2.0-flash'
        }
      }
    }
  ];

  for (const test of configTests) {
    try {
      log(`Testing ${test.name}...`);
      
      const payload = {
        messages: [{ type: 'human', content: 'Test configuration' }],
        config: test.config
      };
      
      const response = await fetch(`${TYPESCRIPT_BASE_URL}/api/run`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
        timeout: TEST_TIMEOUT
      });
      
      if (response.ok) {
        log(`✅ ${test.name} test passed`);
        addResult(`Configuration - ${test.name}`, 'passed');
      } else {
        log(`❌ ${test.name} test failed`);
        const errorData = await response.json();
        addResult(`Configuration - ${test.name}`, 'failed', {
          error: errorData
        });
      }
      
    } catch (error) {
      log(`❌ ${test.name} error: ${error.message}`);
      addResult(`Configuration - ${test.name}`, 'failed', {
        error: error.message
      });
    }
  }
}

/**
 * Generate test report
 */
function generateReport() {
  log('Generating test report...');
  
  const reportPath = path.join(__dirname, 'migration-verification-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  
  // Console summary
  console.log('\n' + '='.repeat(60));
  console.log('MIGRATION VERIFICATION REPORT');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${results.summary.total}`);
  console.log(`Passed: ${results.summary.passed} ✅`);
  console.log(`Failed: ${results.summary.failed} ❌`);
  console.log(`Skipped: ${results.summary.skipped} ⏭️`);
  
  const successRate = ((results.summary.passed / results.summary.total) * 100).toFixed(1);
  console.log(`Success Rate: ${successRate}%`);
  
  if (results.summary.failed > 0) {
    console.log('\nFAILED TESTS:');
    results.tests
      .filter(test => test.status === 'failed')
      .forEach(test => {
        console.log(`❌ ${test.name}: ${test.error || 'Unknown error'}`);
      });
  }
  
  console.log(`\nDetailed report saved to: ${reportPath}`);
  console.log('='.repeat(60));
  
  return results.summary.failed === 0;
}

/**
 * Main test runner
 */
async function runVerification() {
  log('Starting Migration Verification...');
  
  try {
    // Test server availability
    const tsAvailable = await testServerAvailability(TYPESCRIPT_BASE_URL, 'TypeScript');
    
    if (!tsAvailable) {
      log('❌ TypeScript server not available. Skipping tests.');
      addResult('Server Availability Check', 'failed', {
        error: 'TypeScript server not available'
      });
      return generateReport();
    }
    
    // Run all tests
    await testApiCompatibility();
    await testAgentFunctionality();
    await testStreamingFunctionality();
    await testConfigurationHandling();
    
    // Generate report
    const allPassed = generateReport();
    
    if (allPassed) {
      log('🎉 All tests passed! Migration verification successful.');
      process.exit(0);
    } else {
      log('⚠️ Some tests failed. Check the report for details.');
      process.exit(1);
    }
    
  } catch (error) {
    log(`❌ Verification failed with error: ${error.message}`, 'ERROR');
    addResult('Verification Process', 'failed', {
      error: error.message
    });
    generateReport();
    process.exit(1);
  }
}

// Run verification if script is executed directly
if (require.main === module) {
  runVerification();
}

module.exports = {
  runVerification,
  testServerAvailability,
  testApiCompatibility,
  testAgentFunctionality,
  testStreamingFunctionality,
  testConfigurationHandling
};
