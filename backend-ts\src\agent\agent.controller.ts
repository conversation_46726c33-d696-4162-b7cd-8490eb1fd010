import { Controller, Post, Body, HttpException, HttpStatus, UsePipes } from '@nestjs/common';
import { GraphService } from './graph.service';
import { OverallState } from './state';
import { AgentRunRequestDto, AgentRunRequestSchema, AgentResponseDto } from './dto/agent.dto';
import { ZodValidationPipe } from '../common/validation.pipe';
import { HumanMessage, AIMessage, SystemMessage, BaseMessage } from '@langchain/core/messages';

/**
 * Controller for the agent API endpoints.
 */
@Controller('api')
export class AgentController {
  constructor(private readonly graphService: GraphService) {}

  /**
   * Endpoint to run the agent with a given set of messages.
   * This is the main entry point for the frontend to interact with the agent.
   * Includes proper validation using Zod schemas.
   */
  @Post('run')
  @UsePipes(new ZodValidationPipe(AgentRunRequestSchema))
  async runAgent(@Body() body: AgentRunRequestDto): Promise<AgentResponseDto> {
    try {
      // Convert messages to LangChain BaseMessage format
      const convertedMessages: BaseMessage[] = body.messages.map(msg => {
        switch (msg.type) {
          case 'human':
            return new HumanMessage(msg.content);
          case 'ai':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // Initialize the agent state with configuration from request
      const config = body.config?.configurable || {};
      const initialState: OverallState = {
        messages: convertedMessages,
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: config.initial_search_query_count || 3,
        max_research_loops: config.max_research_loops || 2,
        research_loop_count: 0,
        reasoning_model: config.reasoning_model || 'gemini-2.5-pro-preview-05-06',
      };

      // Run the agent using the graph with configuration
      const graph = this.graphService.getGraph();
      const result = await graph.invoke(initialState, {
        configurable: config,
      });

      return { result };
    } catch (error) {
      // Enhanced error handling with proper logging
      const message = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Error in agent run:', error);
      throw new HttpException(
        {
          message,
          timestamp: new Date().toISOString(),
          path: '/api/run',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
